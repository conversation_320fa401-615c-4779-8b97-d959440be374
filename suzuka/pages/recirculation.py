import json
import logging
import operator
from functools import reduce
from typing import Callable, Literal, Optional, TypedDict, Union

from django.contrib.sites.models import Site
from django.core.serializers.json import DjangoJSONEncoder
from django.db.models import Q

from suzuka.classifieds.utils import get_ads, get_category_by_slug, get_site_id
from suzuka.conf.models import Settings
from suzuka.conf.sites import current_site
from suzuka.pages.models import Page
from suzuka.stories import utils
from suzuka.stories.utils import (
    get_canonical_site_details,
    has_lead_image,
    serialise_story_for_view,
)

logger = logging.getLogger(__name__)

RECOMMENDED_STRAPS_TIMEOUT = 60 * 5  # 5 minutes
DEFAULT_STORIES_LIMIT = 4
RECIRC_STORY_LIMIT_DEFAULT = 4

RecircContentTypes = Literal[
    "classifieds", "classifiedlist", "storylist", "ugclist"
]


class RecircContext(TypedDict):
    conf_settings: dict
    features: object
    page: Optional[Page]
    static_url: str
    story_layout: str


class RecircContextObject:
    """Recirc context translated from dict to object"""

    conf_settings: dict
    features: object
    page: Optional[Page]
    static_url: str
    story_layout: str
    current_ugc_id: Optional[int]

    # TODO: In python 3.11, could eventually use Unpack[RecircContext] as entries type
    def __init__(self, **entries):
        self.__dict__.update(entries)


class RecircBaseResult(TypedDict):
    name: str
    stories: list[dict]
    url: str


class RecircResultStoryList(RecircBaseResult):
    content_type: Literal["storylist"]
    show_canonical_site: bool


class RecircResultUgcList(RecircBaseResult):
    content_type: Literal["ugclist"]


class RecircResultClassifiedList(RecircBaseResult):
    content_type: Literal["classifiedlist"]


class RecircResultClassifieds(RecircBaseResult):
    # NOTE: uses classifiedlist content type as no difference to Monaco.
    content_type: Literal["classifiedlist"]


RecircResult = Union[
    RecircResultStoryList,
    RecircResultUgcList,
    RecircResultClassifiedList,
    RecircResultClassifieds,
]


class RecircDetails(TypedDict, total=False):
    cat_slug: str
    limit: int
    page_name: str
    show_canonical_site: bool
    subcat_slug: str
    title: str
    type: RecircContentTypes


class RecircEntry(TypedDict):
    condition: Callable[[RecircContextObject], bool]
    sections: tuple[RecircDetails, ...]


WHATS_ON: RecircDetails = {
    "page_name": "What's On",
    "limit": 3,
    "title": "What's On",
    "type": "ugclist",
}
OUR_STORIES: RecircDetails = {
    "page_name": "Our Stories",
    "limit": 9,
    "title": "Our stories",
}
OUR_PEOPLE: RecircDetails = {
    "page_name": "Our People",
    "limit": 9,
    "title": "Our people",
}
LOCAL_PARTNERS: RecircDetails = {
    "page_name": "Local Partners",
    "limit": 9,
    "title": "Local partners",
}
TRIBUTES_AND_FUNERALS: RecircDetails = {
    "cat_slug": "tributes-funerals",
    "limit": 4,
    "subcat_slug": "notices",
    "title": "Tributes & Funerals Notices",
    "type": "classifieds",
}
LOCAL_SPORT: RecircDetails = {
    "limit": 3,
    "page_name": "Local Sport",
    "title": "From local sport",
}
LOCAL_NEWS: RecircDetails = {
    "limit": 3,
    "page_name": "Local News",
    "title": "From local news",
}
SPORT: RecircDetails = {
    "page_name": "Sport",
    "title": "More from sports",
}

LOCAL_SECTIONS: tuple[RecircDetails, ...] = (
    TRIBUTES_AND_FUNERALS,
    OUR_PEOPLE,
)


COMMUNITY_SECTIONS: tuple[RecircDetails, ...] = (
    WHATS_ON,
    TRIBUTES_AND_FUNERALS,
    OUR_PEOPLE,
)

RECIRCULATIONS: dict[str, RecircEntry] = {
    "community": {
        "condition": lambda ctx: (
            getattr(
                ctx.features, "communityrecirculationfeature_enabled", False
            )
            and ctx.page is not None
            and ctx.page.template.startswith("community/")
        ),
        "sections": COMMUNITY_SECTIONS,
    },
    "local-news": {
        "condition": lambda ctx: bool(
            getattr(
                ctx.features, "communityrecirculationfeature_enabled", False
            )
            and ctx.page
            and ctx.page.name == "Local News"
        ),
        "sections": LOCAL_SECTIONS,
    },
    "local-sport": {
        "condition": lambda ctx: bool(
            getattr(
                ctx.features, "communityrecirculationfeature_enabled", False
            )
            and ctx.page
            and ctx.page.name == "Local Sport"
        ),
        "sections": LOCAL_SECTIONS,
    },
    "mop": {
        "condition": lambda ctx: bool(
            ctx.story_layout == Settings.STORY_LAYOUT_MOP
            or (
                ctx.story_layout == Settings.STORY_LAYOUT_MULTIPLE
                and ctx.conf_settings.get("pianofeature_enable_piano_a_b_test")
            )
        ),
        "sections": (
            {
                "page_name": "Local News",
                "title": "More local stories",
            },
            {
                "page_name": "My Region",
                "title": "More from my region",
                "show_canonical_site": True,
            },
            {
                "page_name": "National",
                "title": "More national stories",
            },
        ),
    },
    "sport": {
        "condition": lambda ctx: (
            ctx.page is not None and ctx.page.template.startswith("sport_")
        ),
        "sections": (
            {
                "page_name": "AFL",
                "title": "More from AFL",
            },
            {
                "page_name": "Sport",
                "title": "More from sports",
            },
        ),
    },
}


class RecircHandler:
    ctx: RecircContextObject
    page_map: dict[str, Page] = {}
    site: Optional[Site] = None

    def __init__(self, ctx: RecircContext) -> None:
        self.ctx = RecircContextObject(**ctx)

    def process_storylist_recirc(
        self,
        recirc_details: RecircDetails,
    ) -> Optional[RecircResultStoryList]:
        limit = recirc_details.get("limit", RECIRC_STORY_LIMIT_DEFAULT)
        show_canonical_site = recirc_details.get("show_canonical_site", False)

        if not (page_name := recirc_details.get("page_name")):
            return None
        try:
            page = self.page_map[page_name.lower()]
        except KeyError:
            return None

        story_list = page.story_list
        if story_list is None:
            return None

        stories: list[dict] = []

        for story in story_list.stories():
            story_dict = serialise_story_for_view(
                story, story_list=story_list, site=self.site
            )
            if has_lead_image(story_dict):
                if show_canonical_site:
                    if canonical_site_details := get_canonical_site_details(
                        story, self.ctx.static_url
                    ):
                        story_dict |= canonical_site_details
                        stories.append(story_dict)
                else:
                    stories.append(story_dict)

            if len(stories) == limit:
                break

        if stories:
            return {
                "content_type": "storylist",
                "name": recirc_details.get("title", ""),
                "show_canonical_site": show_canonical_site,
                "stories": stories,
                "url": page.url or "",
            }

        return None

    def process_ugclist_recirc(
        self,
        recirc_details: RecircDetails,
    ) -> Optional[RecircResultUgcList]:
        limit = recirc_details.get("limit", RECIRC_STORY_LIMIT_DEFAULT)

        if not (page_name := recirc_details.get("page_name")):
            return None
        try:
            page = self.page_map[page_name.lower()]
        except KeyError:
            return None

        stories: list[dict] = []

        ugc_list_zone_item = (
            page.zone_items.filter(element_type="ugclist", zone="main")
            .order_by("order")
            .first()
        )
        if ugc_list_zone_item:
            current_ugc_id = getattr(self.ctx, "current_ugc_id", None)
            for ugc in ugc_list_zone_item.element().ugc(
                limit=limit,
                filters={"id_not_in": str(current_ugc_id)},
            ):
                filtered_ugc = {
                    k: v for k, v in ugc.items() if not k.startswith("_")
                }
                stories.append(filtered_ugc)
                if len(stories) >= limit:
                    break

        if stories:
            return {
                "content_type": "ugclist",
                "name": recirc_details.get("title", ""),
                "stories": stories,
                "url": page.url or "",
            }

        return None

    def process_classifiedlist_recirc(
        self,
        recirc_details: RecircDetails,
    ) -> Optional[RecircResultClassifiedList]:
        limit = recirc_details.get("limit", RECIRC_STORY_LIMIT_DEFAULT)

        if not (page_name := recirc_details.get("page_name")):
            return None
        try:
            page = self.page_map[page_name.lower()]
        except KeyError:
            return None

        stories: list[dict] = []

        classified_list_zone_item = (
            page.zone_items.filter(element_type="classifiedlist", zone="main")
            .order_by("order")
            .first()
        )

        if classified_list_zone_item:
            for classified in classified_list_zone_item.element().classifieds(
                limit=limit
            ):
                filtered_classified = {
                    k: v
                    for k, v in classified.items()
                    if not k.startswith("_")
                }
                stories.append(filtered_classified)

        if stories:
            return {
                "content_type": "classifiedlist",
                "name": recirc_details.get("title", ""),
                "stories": stories,
                "url": page.url or "",
            }

        return None

    def process_classifieds_recirc(
        self,
        recirc_details: RecircDetails,
    ) -> Optional[RecircResultClassifieds]:
        limit = recirc_details.get("limit", RECIRC_STORY_LIMIT_DEFAULT)

        if not (cat_slug := recirc_details.get("cat_slug")) or not (
            subcat_slug := recirc_details.get("subcat_slug")
        ):
            return None

        site_id = get_site_id(self.site)

        if not (category := get_category_by_slug(site_id, cat_slug)) or (
            subcat_slug
            and not (
                subcategory := get_category_by_slug(
                    site_id, subcat_slug, cat_slug
                )
            )
        ):
            return None

        target_category_id = (
            subcategory["id"]
            if subcat_slug and subcategory
            else category["id"]
        )

        ads, ad_count = get_ads(
            site_id, target_category_id, page=1, page_size=limit
        )

        if ad_count:
            serialized_ads = json.loads(
                json.dumps(
                    ads,
                    cls=DjangoJSONEncoder,
                ),
            )

            return {
                "content_type": "classifiedlist",
                "name": recirc_details.get("title", ""),
                "stories": serialized_ads,
                "url": f"classifieds/{cat_slug}/{subcat_slug}",
            }

        return None

    def process_entry(
        self, recirc_name: str, recirc_entry: RecircEntry
    ) -> list[RecircResult]:
        site = current_site()
        self.site = site
        cache_key = f"{recirc_name}_recommended_straps_{site.pk}"
        # sections: Optional[list[RecircResult]] = utils.cache_get(cache_key)

        # if sections is not None:
        #     current_ugc_id = getattr(self.ctx, "current_ugc_id", None)
        #     if current_ugc_id:
        #         ugc_in_list = any(
        #             story.get("id") == current_ugc_id
        #             for section in sections
        #             if section.get("content_type") == "ugclist"
        #             for story in section.get("stories", ())
        #         )
        #         if ugc_in_list:
        #             sections = None  # rebuild if current ugc is in the ugclist section

        # if sections is not None:
        #     return sections

        try:
            section_details = recirc_entry["sections"]
        except KeyError:
            return []

        page_names = set(
            page_name
            for section_detail in section_details
            if (page_name := section_detail.get("page_name"))
        )

        page_name_filter = reduce(
            operator.or_,
            [Q(name__iexact=page_name) for page_name in page_names],
            Q(),
        )

        pages = (
            Page.objects.filter(
                sites=site,
                draft=False,
                accessible=True,
            )
            .filter(page_name_filter)
            .select_related("story_list")
        )

        self.page_map = {page.name.lower(): page for page in pages}

        sections = []

        for recirc_details in section_details:
            content_type = recirc_details.get("type", "storylist")
            try:
                method = {
                    "classifiedlist": self.process_classifiedlist_recirc,
                    "classifieds": self.process_classifieds_recirc,
                    "storylist": self.process_storylist_recirc,
                    "ugclist": self.process_ugclist_recirc,
                }[content_type]
            except KeyError:
                continue

            if recirc := method(recirc_details):
                sections.append(recirc)

        utils.cache_set(
            cache_key,
            sections,
            timeout=RECOMMENDED_STRAPS_TIMEOUT,
        )

        return sections

    def process(self) -> list[RecircResult]:
        for recirc_name, recirc_entry in RECIRCULATIONS.items():
            if recirc_entry["condition"](self.ctx):
                return self.process_entry(recirc_name, recirc_entry)
        return []


def get_all_recirculation_sections(
    context: RecircContext,
) -> list[RecircResult]:
    return RecircHandler(context).process()
