import drafting
import reversion
from bleach import clean
from django.conf import settings
from django.contrib.postgres.fields import A<PERSON>yField
from django.contrib.sites.models import Site
from django.core.validators import (
    MaxValueValidator,
    MinValueValidator,
    RegexValidator,
)
from django.db import models
from django.http import Http404
from django.utils.translation import gettext_lazy as _
from model_utils import Choices

from suzuka.classifieds.models import ClassifiedList as ClassifiedListSpec
from suzuka.conf.sites import InvalidDomain, current_site
from suzuka.dailymotion.utils import get_storylist as get_dm_storylist
from suzuka.elements_demo.fields import PublishDateField
from suzuka.pages.fields import AddableForeignKey
from suzuka.pages.models import Element
from suzuka.pages.utils import get_theme_dir
from suzuka.partners.fields import MultiSelectField
from suzuka.stories.models import StoryList as StoryListSpec
from suzuka.stories.utils import serialise_story_for_view
from suzuka.ugc.models import UGCList as UGCListSpec

from .settings import (
    CODE_SNIPPET_ALLOWED_ATTRIBUTES,
    CODE_SNIPPET_ALLOWED_TAGS,
)
from .validators import validate_code

IFRAME_ALLOW_CHOICES = (
    ("autoplay", "AutoPlay"),
    ("camera", "Camera"),
    ("fullscreen", "Fullscreen"),
    ("geolocation", "Geolocation"),
    ("microphone", "Microphone"),
    ("speaker", "Speaker"),
)

PUBLISH_DATE_LABELS = [
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
    "Sunday",
]

PUBLISH_DATE_CHOICES = (
    ("0", "All day"),
    ("1", "Business hours"),
    ("2", "After hours"),
    ("3", "Do not publish"),
)


def is_changed(obj):
    """
    Return ``True`` if editing is active and the object has changed.

    """
    edits = getattr(drafting._edit_mode, "edits", None)
    saves_for = getattr(edits, "saves_for", lambda x: {})
    return obj.id in saves_for(obj)


@reversion.register()
class Heading(Element):  # type: ignore[django-manager-missing]
    heading = models.CharField(max_length=255)
    url = models.URLField(
        max_length=255,
        blank=True,
        verbose_name=_("URL"),
    )
    open_new_window = models.BooleanField(
        default=False, verbose_name=_("Open in a new window")
    )

    subtitle = models.CharField(
        max_length=1000,
        blank=True,
        default="",
    )
    no_follow = models.BooleanField(
        default=False,
        verbose_name=_("No follow"),
        help_text=_("Tell search engines not to follow the link"),
    )
    anchor_id = models.CharField(
        max_length=255,
        blank=True,
        verbose_name=_("Anchor ID"),
        help_text=_(
            "Set this slugified (no spaces, no special characters) heading as an anchor element to scroll to"
        ),
    )

    def __str__(self):
        return self.heading


@reversion.register()
class TextBlock(Element):  # type: ignore[django-manager-missing]
    text = models.TextField()

    def __str__(self):
        return self.text


@reversion.register()
class StoryList(Element):  # type: ignore[django-manager-missing]
    story_list = AddableForeignKey(
        "stories.StoryList",
        null=True,
        related_name="story_elements",
        on_delete=models.deletion.CASCADE,
    )
    url_params = models.CharField(null=True, blank=True, max_length=200)
    label = models.CharField(blank=True, max_length=200)
    limit = models.PositiveIntegerField(null=True, blank=True)
    pinned_stories_only = models.BooleanField(default=False)
    offset = models.PositiveIntegerField(
        default=0,
        verbose_name="Story offset",
        help_text=_(
            "The offset refers to the position that the storylist will begin"
        ),
        validators=[MinValueValidator(0)],
    )

    CHOICES = [
        ("E", "Enable (only show summary that is enabled in Newsnow)"),
        ("A", "Always show (override settings in Newsnow)"),
        ("D", "Disabled (hide summary; override settings in NewsNow)"),
    ]

    summary_options = models.CharField(
        verbose_name="Summary: select option for story list",
        max_length=1,
        choices=CHOICES,
        default="E",
    )

    large_lead_story = models.BooleanField(default=False)
    flip_story_display = models.BooleanField(
        default=True,
        verbose_name="Flip story display",
        help_text=_("Put the large story on the right"),
    )
    is_hero_image = models.BooleanField(default=False)

    allow_ads = models.BooleanField(
        default=False,
        verbose_name="allow ads",
        help_text=_("Display ads if the layout supports it"),
    )
    show_blc = models.BooleanField(
        default=False,
        verbose_name="show business listing card",
        help_text=_("Display business listing card if the layout supports it"),
    )
    use_canonical_url = models.BooleanField(
        default=False,
        verbose_name=_("Use canonical URL"),
        help_text=_(
            "Use the story's canonical URL instead of opening on this "
            "site, and open in a new window."
        ),
    )
    show_canonical_site = models.BooleanField(
        default=False,
        verbose_name=_("Show canonical site"),
        help_text=_("Display the canonical site logo and name of the story"),
    )

    disable_bottom_border = models.BooleanField(
        default=False,
        verbose_name=_("Disable bottom border"),
        help_text=_("Hide the bottom border line"),
    )

    TYPE_CHOICES = Choices(
        ("DM", "DM", _("Dailymotion")),
        ("NA", "NA", _("Default")),
    )
    list_type = models.CharField(
        choices=TYPE_CHOICES,
        default=TYPE_CHOICES.NA,
        max_length=2,
        db_index=True,
        help_text="Select a type of storylist to display. Default uses Silverstone stories",
    )

    dpe_id = models.CharField(
        max_length=255,
        blank=True,
        verbose_name=_("DPE ID"),
        help_text=_("Select a DPE ID to display"),
    )
    dpe_publish_time = models.CharField(
        _("DPE Publish Time (when issue allowed to be viewed)"),
        max_length=4,
        validators=[
            RegexValidator(
                regex="^(?:([01]\d|2[0-3])([0-5]\d)|2400)$",
                message="Must be 4 digits in 24hr hhmm format",
                code="nomatch",
            )
        ],
        blank=False,
        default="0600",
        help_text="Use 24hr hhmm format",
    )

    popular_story_list = AddableForeignKey(
        "stories.StoryList",
        null=True,
        blank=True,
        related_name="popular_story_elements",
        on_delete=models.deletion.SET_NULL,
    )

    def __str__(self):
        return str(self.story_list)

    def stories(self, limit=None, use_cache=True, site_id=None, request=None):
        if self.list_type == self.TYPE_CHOICES.DM:
            site = (
                current_site()
                if site_id is None
                else Site.objects.select_related("settings").get(id=site_id)
            )
            page = None
            if request:
                query_params = request.GET
                try:
                    val = query_params.get("page")
                    if val:
                        page = int(val)
                except ValueError:
                    pass
            try:
                story_list = get_dm_storylist(
                    owner_id=site.settings.dailymotionfeature_owner_id,
                    element=self,
                    page=page,
                )
            except Http404:
                return []
            return story_list
        else:
            try:
                # Don't used cached ``stories.StoryList`` if editing
                # because the pinned stories may have changed.
                if is_changed(self) or not use_cache:
                    story_list = StoryListSpec.objects.get(
                        pk=self.story_list_id
                    )
                else:
                    story_list = self.story_list
                    assert story_list is not None
            except (models.ObjectDoesNotExist, AssertionError):
                return []

            # To do:  need to use offset and limit after the silverstone enable the offset function
            limit = (
                limit or self.limit if get_theme_dir() != "autumn" else limit
            )
            return story_list.stories(
                limit,
                ids_only=self.pinned_stories_only,
                use_cache=use_cache,
                site_id=site_id,
            )

    def popular_stories(self, limit=None, use_cache=True, site_id=None):
        site = (
            current_site()
            if site_id is None
            else Site.objects.select_related("settings").get(id=site_id)
        )

        if self.popular_story_list:
            return [
                serialise_story_for_view(
                    story, self.popular_story_list, site=site
                )
                for story in self.popular_story_list.stories(
                    use_cache=use_cache, site_id=site.id, limit=limit
                )
            ]
        else:
            return []

    def commit(self):
        super().commit()
        if self.story_list:
            self.story_list.save()


@reversion.register()
class ClusteredStoryList(Element):  # type: ignore[django-manager-missing]
    story_list = AddableForeignKey(
        "stories.StoryList",
        null=True,
        related_name="clustered_story_elements",
        on_delete=models.deletion.CASCADE,
    )
    from_organisation = models.CharField(
        blank=False,
        max_length=200,
        help_text=_("Name of organisation content is from"),
    )
    offset = models.PositiveIntegerField(
        default=1,
        verbose_name="Story offset",
        help_text=_(
            "The offset refers to the position that the storylist will begin"
        ),
        validators=[MinValueValidator(1)],
    )
    limit = models.PositiveIntegerField(
        null=True,
        blank=True,
        default=1,
        validators=[MinValueValidator(1), MaxValueValidator(20)],
    )
    page = models.ForeignKey(
        "pages.Page",
        null=True,
        blank=True,
        related_name="clustered_story_lists",
        on_delete=models.deletion.CASCADE,
    )
    use_canonical_url = models.BooleanField(
        default=False,
        verbose_name=_("Use canonical URL"),
        help_text=_(
            "Use the story's canonical URL instead of opening on this "
            "site, and open in a new window."
        ),
    )

    def __str__(self):
        return str(self.story_list)

    def stories(self, limit=None, use_cache=True, site_id=None):
        try:
            # Don't used cached ``stories.StoryList`` if editing
            # because the pinned stories may have changed.
            if is_changed(self) or not use_cache:
                story_list = StoryListSpec.objects.get(pk=self.story_list_id)
            else:
                story_list = self.story_list
                assert story_list is not None
        except (models.ObjectDoesNotExist, AssertionError):
            return []
        return story_list.stories(
            limit=self.offset + (limit or self.limit),
            use_cache=use_cache,
            site_id=site_id,
        )

    def commit(self):
        super().commit()
        if self.story_list:
            self.story_list.save()


@reversion.register()
class TitledStoryList(Element):  # type: ignore[django-manager-missing]
    story_list = AddableForeignKey(
        "stories.StoryList",
        null=True,
        related_name="titled_story_elements",
        on_delete=models.deletion.CASCADE,
    )
    title = models.CharField(blank=False, max_length=200)
    url_params = models.CharField(blank=True, max_length=200)
    limit = models.PositiveIntegerField(null=True, blank=True)
    pinned_stories_only = models.BooleanField(default=False)
    allow_ads = models.BooleanField(
        default=False,
        verbose_name="allow ads",
        help_text=_("Display ads if the layout supports it"),
    )
    show_blc = models.BooleanField(
        default=False,
        verbose_name="show business listing card",
        help_text=_("Display business listing card if the layout supports it"),
    )
    use_canonical_url = models.BooleanField(
        default=False,
        verbose_name=_("Use canonical URL"),
        help_text=_(
            "Use the story's canonical URL instead of opening on this "
            "site, and open in a new window."
        ),
    )

    def __str__(self):
        return str(self.story_list)

    def stories(self, limit=None, use_cache=True, site_id=None):
        try:
            # Don't used cached ``stories.StoryList`` if editing
            # because the pinned stories may have changed.
            if is_changed(self) or not use_cache:
                story_list = StoryListSpec.objects.get(pk=self.story_list_id)
            else:
                story_list = self.story_list
                assert story_list is not None
        except (models.ObjectDoesNotExist, AssertionError):
            return []
        return story_list.stories(
            limit=limit or self.limit,
            ids_only=self.pinned_stories_only,
            use_cache=use_cache,
            site_id=site_id,
        )

    def commit(self):
        super().commit()
        if self.story_list:
            self.story_list.save()


@reversion.register()
class DPEList(Element):  # type: ignore[django-manager-missing]
    DATE_W_D_M_Y = "w_d_m_y"
    DATE_M_Y = "m_y"

    DATE_CHOICES = (
        (DATE_W_D_M_Y, _("Monday, 12 January 2020")),
        (DATE_M_Y, _("January 2020")),
    )

    limit = models.PositiveIntegerField(
        default=9, verbose_name="Items per page"
    )
    dpe_id = models.CharField(max_length=10, verbose_name="DPE ID", blank=True)
    use_canonical_url = False
    date_format = models.CharField(
        verbose_name="Date format",
        max_length=50,
        choices=DATE_CHOICES,
        default=DATE_W_D_M_Y,
        help_text=_("Datetime display format of the DPE issue"),
    )


@reversion.register()
class EMagList(Element):  # type: ignore[django-manager-missing]
    limit = models.PositiveIntegerField(
        default=9, verbose_name="Items per page"
    )


@reversion.register()
class DPECard(Element):  # type: ignore[django-manager-missing]
    pass


@reversion.register()
class MenuList(Element):  # type: ignore[django-manager-missing]
    page = models.ForeignKey(
        "pages.Page",
        null=True,
        related_name="menu_lists",
        on_delete=models.deletion.CASCADE,
    )

    title = models.CharField(
        help_text=_("Title will replace the page heading if it is not blank"),
        max_length=100,
        blank=True,
        default="",
    )

    subtitle = models.CharField(
        max_length=1000,
        blank=True,
        default="",
    )

    def __str__(self):
        return str(self.page)


@reversion.register()
class Iframe(Element):  # type: ignore[django-manager-missing]
    url = models.URLField(max_length=255, verbose_name=_("URL"))
    style = models.CharField(
        max_length=1024,
        blank=True,
        default="border: 0;",
        help_text=_("Optional. Custom CSS for this iframe element."),
    )
    allow = MultiSelectField(
        "Allow",
        help_text=_("Allow feature policy options for the iframe."),
        blank=True,
        null=True,
        max_length=1024,
        choices=IFRAME_ALLOW_CHOICES,
    )
    auto_refresh = models.BooleanField(
        "Auto refresh",
        default=False,
        help_text=_(
            "If checked, iframe will auto refresh every 5 mins. "
            "Useful for charts that update with data source."
        ),
    )

    def __str__(self):
        return self.url


@reversion.register()
class CodeSnippet(Element):  # type: ignore[django-manager-missing]
    code = models.TextField(validators=[validate_code])

    def __str__(self):
        """Get the code with `{{ template_variables }}` substituted."""
        code = self.code

        try:
            site = current_site()
            static_url = settings.STATIC_URL
            if not static_url:
                return code
        except InvalidDomain:
            return code

        replacements = {
            # Site's domain without the `www.` prefix
            "root_domain": lambda s: s.domain.replace("www.", ""),
            "static_url": lambda s: static_url,
        }

        for var, replacement in list(replacements.items()):
            code = code.replace("{{ %s }}" % var, replacement(site))

        return code

    def save(self, *args, **kwargs):
        """
        Remove potentially dangerous HTML tags and attributes.
        """
        self.code = clean(
            self.code,
            CODE_SNIPPET_ALLOWED_TAGS,
            CODE_SNIPPET_ALLOWED_ATTRIBUTES,
            strip=True,
        )
        super().save(*args, **kwargs)


@reversion.register()
class Advertisement(Element):  # type: ignore[django-manager-missing]
    position = models.PositiveSmallIntegerField(
        default=0,
        help_text="Set to zero for automatic numbering",
    )

    @property
    def skip_position(self):
        """Determine whether to skip automatic position numbering."""
        return self.position != 0


@reversion.register()
class Classified(Element):  # type: ignore[django-manager-missing]
    NUM_ITEM_CHOICES = (
        (3, 3),
        (6, 6),
        (9, 9),
        (12, 12),
    )
    number_of_items = models.PositiveSmallIntegerField(
        default=3,
        choices=NUM_ITEM_CHOICES,
        help_text="The number of items for the template to show by default.",
    )


@reversion.register()
class Navigation(Element):  # type: ignore[django-manager-missing]
    BRAND_COLOR_CHOICES = (
        ("white", "White"),
        ("black", "Black"),
        ("custom", "Custom"),
    )
    FONT_COLOR_CHOICES = (("dark", "Dark"), ("light", "Light"))
    BRAND_IMAGE_CHOICES = (("standard", "Standard"), ("reversed", "Reversed"))

    brand_color = models.CharField(
        max_length=50,
        choices=BRAND_COLOR_CHOICES,
        blank=False,
        default="white",
    )
    custom_color = models.CharField(max_length=50, blank=True)
    font_color = models.CharField(
        max_length=20, choices=FONT_COLOR_CHOICES, blank=False, default="dark"
    )
    brand_image = models.CharField(
        max_length=20,
        choices=BRAND_IMAGE_CHOICES,
        blank=False,
        default="standard",
    )
    est_year = models.CharField(max_length=4, blank=True)
    display_shortcuts_at_top = models.BooleanField(blank=True, default=False)
    shortcut_1_label = models.CharField(max_length=50, blank=True)
    shortcut_1_description = models.CharField(max_length=100, blank=True)
    shortcut_1_url = models.CharField(max_length=200, blank=True)
    shortcut_1_icon = models.CharField(max_length=200, blank=True)
    shortcut_1_cta = models.CharField(max_length=50, blank=True)
    shortcut_1_badge = models.CharField(max_length=20, blank=True)
    shortcut_1_new_window = models.BooleanField(blank=True, default=False)
    shortcut_2_label = models.CharField(max_length=50, blank=True)
    shortcut_2_description = models.CharField(max_length=100, blank=True)
    shortcut_2_url = models.CharField(max_length=200, blank=True)
    shortcut_2_icon = models.CharField(max_length=200, blank=True)
    shortcut_2_cta = models.CharField(max_length=50, blank=True)
    shortcut_2_badge = models.CharField(max_length=20, blank=True)
    shortcut_2_new_window = models.BooleanField(blank=True, default=False)
    shortcut_3_label = models.CharField(max_length=50, blank=True)
    shortcut_3_description = models.CharField(max_length=100, blank=True)
    shortcut_3_url = models.CharField(max_length=200, blank=True)
    shortcut_3_icon = models.CharField(max_length=200, blank=True)
    shortcut_3_cta = models.CharField(max_length=50, blank=True)
    shortcut_3_new_window = models.BooleanField(blank=True, default=False)
    shortcut_3_badge = models.CharField(max_length=20, blank=True)
    shortcut_4_label = models.CharField(max_length=50, blank=True)
    shortcut_4_description = models.CharField(max_length=100, blank=True)
    shortcut_4_url = models.CharField(max_length=200, blank=True)
    shortcut_4_icon = models.CharField(max_length=200, blank=True)
    shortcut_4_cta = models.CharField(max_length=50, blank=True)
    shortcut_4_new_window = models.BooleanField(blank=True, default=False)
    shortcut_4_badge = models.CharField(max_length=20, blank=True)
    external_link_1_text = models.CharField(max_length=50, blank=True)
    external_link_1_url = models.URLField(blank=True)
    external_link_1_new_window = models.BooleanField(
        help_text="Open in a new window", blank=True, default=False
    )
    external_link_1_icon = models.CharField(max_length=200, blank=True)
    external_link_2_text = models.CharField(max_length=50, blank=True)
    external_link_2_url = models.URLField(blank=True)
    external_link_2_new_window = models.BooleanField(
        help_text="Open in a new window", blank=True, default=False
    )
    external_link_2_icon = models.CharField(max_length=200, blank=True)
    external_link_3_text = models.CharField(max_length=50, blank=True)
    external_link_3_url = models.URLField(blank=True)
    external_link_3_new_window = models.BooleanField(
        help_text="Open in a new window", blank=True, default=False
    )
    external_link_3_icon = models.CharField(max_length=200, blank=True)
    external_link_4_text = models.CharField(max_length=50, blank=True)
    external_link_4_url = models.URLField(blank=True)
    external_link_4_new_window = models.BooleanField(
        help_text="Open in a new window", blank=True, default=False
    )
    external_link_4_icon = models.CharField(max_length=200, blank=True)
    external_link_5_text = models.CharField(max_length=50, blank=True)
    external_link_5_url = models.URLField(blank=True)
    external_link_5_new_window = models.BooleanField(
        help_text="Open in a new window", blank=True, default=False
    )
    external_link_5_icon = models.CharField(max_length=200, blank=True)
    advertising_url = models.URLField(blank=True)
    survey_url = models.URLField(blank=True)
    give_feedback = models.BooleanField(
        help_text="Give Feedback", blank=True, default=True
    )
    aap_press_releases = models.BooleanField(
        help_text="Press Releases from AAP", blank=True, default=False
    )
    contact_us = models.BooleanField(
        help_text="Contact us", blank=True, default=True
    )
    help_centre = models.BooleanField(
        help_text="Help Centre", blank=True, default=True
    )
    tc_digital = models.BooleanField(
        help_text="Terms & Conditions - Digital", blank=True, default=True
    )
    tc_newspaper = models.BooleanField(
        help_text="Terms & Conditions - Newspaper", blank=True, default=True
    )
    privacy_policy = models.BooleanField(
        help_text="Privacy Policy", blank=True, default=True
    )
    about_us = models.BooleanField(
        help_text="About us", blank=True, default=True
    )
    conditions_of_use = models.BooleanField(
        help_text="Conditions of Use", blank=True, default=True
    )


@reversion.register()
class MailingList(Element):  # type: ignore[django-manager-missing]
    """A zone item for a mailing list subscription form."""

    heading_text = models.TextField(blank=True)
    text = models.TextField("Support text", blank=True)
    tags = models.CharField(blank=True, max_length=1000)
    subscribe_visible = models.BooleanField(
        "Widget visible",
        default=True,
        help_text=_(
            "If unchecked, widget will not be visible to non-subscribers"
        ),
    )

    form_data = models.CharField(
        blank=True,
        max_length=200,
        help_text="Additional query string data for the form action",
    )
    marketing_cloud_url = models.CharField(
        blank=True,
        max_length=500,
        help_text="The newsletter url of the marketing cloud",
    )

    def __str__(self):
        """Get the string representation."""
        return self.text


class Weather(Element):  # type: ignore[django-manager-missing]
    pass


@reversion.register()
class REVWidget(Element):  # type: ignore[django-manager-missing]
    pass


class SportsHub(Element):  # type: ignore[django-manager-missing]
    # Taken from Monaco: src/types/SportsHub.ts, enum SportPage
    SPORTS_CHOICES = [
        ("afl", "AFL"),
        ("cricket", "Cricket"),
        ("nrl", "NRL"),
        ("a-league", "A-League"),
    ]
    sports = MultiSelectField(
        "Sports",
        choices=SPORTS_CHOICES,
        max_length=512,
        blank=True,
        help_text="Select the sport(s) to display",
    )


class DailyMotion(Element):  # type: ignore[django-manager-missing]
    playlist_id = models.CharField(
        max_length=20,
        blank=True,
        verbose_name=_("Playlist ID"),
        help_text=_("Dailymotion playlist ID for videos"),
    )
    player_id = models.CharField(
        max_length=20,
        default=settings.DEFAULT_DAILYMOTION_PLAYER_ID_FOR_VIDEO_SHORTS,
        blank=True,
        verbose_name=_("Player ID"),
        help_text=_("Dailymotion player ID for video display"),
    )
    number_of_video = models.PositiveIntegerField(null=True, blank=True)
    ad_frequency = models.PositiveIntegerField(
        default=5,
        verbose_name=_("Ads frequency in between slides "),
        help_text=_("Ex: 5 will display Ads in position 5, 10, 15..."),
        blank=True,
        null=True,
    )
    url = models.CharField(
        max_length=512,
        blank=True,
        help_text=_("The url of the view all link will redirect to"),
    )
    publish = PublishDateField(
        labels=PUBLISH_DATE_LABELS,
        blank=True,
        max_length=100,
        choices=PUBLISH_DATE_CHOICES,
    )


@reversion.register()
class Banner(Element):  # type: ignore[django-manager-missing]
    title = models.CharField(blank=True, max_length=255)
    sub_title = models.TextField(blank=True, max_length=500)
    background_url = models.CharField(
        max_length=512,
        blank=True,
        help_text=_("The url of the banner background image"),
    )
    open_new_window = models.BooleanField(
        default=False, verbose_name=_("Open in a new window")
    )
    url = models.CharField(
        max_length=512,
        blank=True,
        help_text=_("The url of the banner button will redirect to"),
    )


@reversion.register()
class ExploreTravelDealList(Element):  # type: ignore[django-manager-missing]
    api_filter_by_id = models.CharField(
        "Filter deals by Id",
        max_length=10,
        blank=True,
        help_text=(
            "Get Deal by Id. Returns 1 result. "
            "Note: When using this filter, other filters are discarded."
        ),
    )
    api_filter_by_destination = models.CharField(
        "Filter deals by destination",
        blank=True,
        max_length=100,
        help_text=(
            "Use `,` to filter by multiple destinations. "
            "Note: an empty value will return deals from all destinations."
        ),
    )
    offset = models.PositiveIntegerField(
        default=1,
        verbose_name="Deals offset",
        help_text=_("The offset refers to the position that deals will begin"),
        validators=[MinValueValidator(1)],
    )


@reversion.register()
class ExploreSimpleLinkWidget(Element):  # type: ignore[django-manager-missing]
    title = models.CharField(
        blank=False, max_length=64, default="Explore our deals today"
    )
    description = models.CharField(
        blank=False,
        max_length=256,
        default="Search by destination & discover our range of tour & accommodation offers. Find your next getaway.",
    )
    button_text = models.CharField(
        blank=False, max_length=64, default="View Deals"
    )
    url = models.CharField(
        max_length=256,
        blank=False,
        default="https://book.exploretravel.com.au/deals-and-offers",
    )


@reversion.register()
class FeaturedDestination(Element):  # type: ignore[django-manager-missing]
    title = models.CharField(
        max_length=64,
        blank=False,
        default="",
    )
    description = models.CharField(
        max_length=64,
        blank=False,
        default="Read The Guide",
    )
    url = models.CharField(
        max_length=256,
        blank=False,
        default="",
    )
    desktop_image = models.CharField(
        max_length=256,
        blank=False,
        default="",
    )
    tablet_image = models.CharField(
        max_length=256,
        blank=False,
        default="",
    )
    mobile_image = models.CharField(
        max_length=256,
        blank=False,
        default="",
    )


@reversion.register()
class Footer(Element):  # type: ignore[django-manager-missing]
    pass


@reversion.register()
class Carousel(Element):  # type: ignore[django-manager-missing]
    title_1 = models.CharField("Slide 1 - Title", max_length=64, blank=True)
    description_1 = models.CharField(
        "Slide 1 - Description", max_length=256, blank=True
    )
    image_1 = models.CharField(
        "Slide 1 - Image Url", max_length=256, blank=True
    )
    url_1 = models.CharField("Slide 1 - Link Url", max_length=256, blank=True)
    open_new_window_1 = models.BooleanField(
        default=False, verbose_name=_("Slide 1 - Open in a new window")
    )

    title_2 = models.CharField("Slide 2 - Title", max_length=64, blank=True)
    description_2 = models.CharField(
        "Slide 2 - Description", max_length=256, blank=True
    )
    image_2 = models.CharField(
        "Slide 2 - Image Url", max_length=256, blank=True
    )
    url_2 = models.CharField("Slide 2 - Link Url", max_length=256, blank=True)
    open_new_window_2 = models.BooleanField(
        default=False, verbose_name=_("Slide 2 - Open in a new window")
    )

    title_3 = models.CharField("Slide 3 - Title", max_length=64, blank=True)
    description_3 = models.CharField(
        "Slide 3 - Description", max_length=256, blank=True
    )
    image_3 = models.CharField(
        "Slide 3 - Image Url", max_length=256, blank=True
    )
    url_3 = models.CharField("Slide 3 - Link Url", max_length=256, blank=True)
    open_new_window_3 = models.BooleanField(
        default=False, verbose_name=_("Slide 3 - Open in a new window")
    )

    title_4 = models.CharField("Slide 4 - Title", max_length=64, blank=True)
    description_4 = models.CharField(
        "Slide 4 - Description", max_length=256, blank=True
    )
    image_4 = models.CharField(
        "Slide 4 - Image Url", max_length=256, blank=True
    )
    url_4 = models.CharField("Slide 4 - Link Url", max_length=256, blank=True)
    open_new_window_4 = models.BooleanField(
        default=False, verbose_name=_("Slide 4 - Open in a new window")
    )


@reversion.register()
class InfoWithCta(Element):  # type: ignore[django-manager-missing]
    title = models.CharField(max_length=256, blank=True)
    description = models.TextField(
        max_length=500, blank=True, help_text="Description max 500 characters"
    )
    button_text = models.CharField(
        max_length=256, blank=False, help_text="CTA button text (required)"
    )
    url = models.CharField(
        max_length=256, blank=False, help_text="CTA button URL (required). Supports both relative (/path) and absolute (https://...) URLs."
    )


@reversion.register()
class Image(Element):  # type: ignore[django-manager-missing]
    alt = models.CharField(max_length=256, blank=True)
    title = models.CharField(max_length=256, blank=True)
    open_new_window = models.BooleanField(
        default=False, verbose_name=_("Open in a new window")
    )
    url = models.CharField(
        max_length=512,
        blank=True,
        help_text=_("The url of the image will redirect to"),
    )
    desktop_image = models.CharField(
        max_length=256,
        blank=False,
        default="",
    )
    tablet_image = models.CharField(
        max_length=256,
        blank=False,
        default="",
    )
    mobile_image = models.CharField(
        max_length=256,
        blank=False,
        default="",
    )


@reversion.register()
class Comments(Element):  # type: ignore[django-manager-missing]
    """A widget for displaying comments."""

    title = models.CharField(max_length=256, blank=True)


@reversion.register()
class PlayHq(Element):  # type: ignore[django-manager-missing]
    organisation_id = models.CharField(max_length=36)
    season_id = models.CharField(max_length=36, blank=True)
    grade_1_id = models.CharField(max_length=36, blank=True)
    grade_2_id = models.CharField(max_length=36, blank=True)
    league_url = models.CharField(max_length=50, blank=True)
    league_name = models.CharField(max_length=100, blank=True)
    league_image_id = models.CharField(max_length=100, blank=True)


@reversion.register()
class Newsletter(Element):  # type: ignore[django-manager-missing]
    heading = models.CharField(max_length=64, blank=True)
    url = models.CharField(max_length=264, blank=True)


@reversion.register()
class ViewJobs(Element):  # type: ignore[django-manager-missing]
    limit = models.PositiveIntegerField(
        default=2,
        help_text=_("Limit the number of listings shown"),
    )
    offset = models.PositiveIntegerField(
        default=1,
        verbose_name="Listing offset",
        help_text=_("The offset in the listings to start from"),
        validators=[MinValueValidator(1)],
    )
    sticky_featured = models.BooleanField(
        default=False,
        help_text=_("Featured listings appear first"),
    )


@reversion.register()
class Traffic(Element):  # type: ignore[django-manager-missing]
    title = models.CharField(max_length=100, blank=True)
    limit = models.PositiveIntegerField(
        default=2,
        help_text=_("Limit the number of listings shown"),
        validators=[MinValueValidator(1), MaxValueValidator(20)],
    )
    range = models.PositiveIntegerField(
        default=50,
        verbose_name="Distance range",
        help_text=_("Search range (e.g., 50km radius)"),
        validators=[MinValueValidator(1)],
    )


@reversion.register()
class ClassifiedList(Element):  # type: ignore[django-manager-missing]
    title = models.CharField(max_length=100, blank=True)
    classified_list = AddableForeignKey(
        "classifieds.ClassifiedList",
        null=True,
        related_name="classified_elements",
        on_delete=models.deletion.CASCADE,
    )
    limit = models.PositiveIntegerField(null=True, blank=True)
    offset = models.PositiveIntegerField(
        default=1,
        verbose_name="Classified offset",
        help_text=_(
            "The offset refers to the position that the classified list will begin"
        ),
        validators=[MinValueValidator(1)],
    )
    pinned_classifieds_only = models.BooleanField(default=False)

    def __str__(self):
        return str(self.classified_list)

    def classifieds(
        self, limit=None, use_cache=True, site_id=None, request=None
    ):
        try:
            # Don't used cached ``classifieds.ClassifiedList`` if editing
            # because the pinned classifieds may have changed.
            if is_changed(self) or not use_cache:
                classified_list = ClassifiedListSpec.objects.get(
                    pk=self.classified_list_id
                )
            else:
                classified_list = self.classified_list
                assert classified_list is not None
        except (models.ObjectDoesNotExist, AssertionError):
            return []

        return classified_list.classifieds(
            limit,
            ids_only=self.pinned_classifieds_only,
            use_cache=use_cache,
            site_id=site_id,
        )

    def commit(self):
        super().commit()
        if self.classified_list:
            self.classified_list.save()


@reversion.register()
class UGCList(Element):  # type: ignore[django-manager-missing]
    title = models.CharField(max_length=100, blank=True)
    ugc_list = AddableForeignKey(
        "ugc.UGCList",
        null=True,
        related_name="ugc_elements",
        on_delete=models.deletion.CASCADE,
    )
    limit = models.PositiveIntegerField(null=True, blank=True)
    offset = models.PositiveIntegerField(
        default=1,
        verbose_name="UGC offset",
        help_text=_(
            "The offset refers to the position that the ugc list will begin"
        ),
        validators=[MinValueValidator(1)],
    )
    pinned_ugc_only = models.BooleanField(default=False)
    show_region_toggle = models.BooleanField(default=False)

    def __str__(self):
        return str(self.ugc_list)

    def ugc(
        self,
        limit=None,
        use_cache=True,
        site_id=None,
        request=None,
        filters=None,
    ):
        try:
            # Don't used cached ``ugc.UGCList`` if editing
            # because the pinned ugc may have changed.
            if is_changed(self) or not use_cache:
                ugc_list = UGCListSpec.objects.get(pk=self.ugc_list_id)
            else:
                ugc_list = self.ugc_list
                assert ugc_list is not None
        except (models.ObjectDoesNotExist, AssertionError):
            return []

        return ugc_list.ugc(
            limit,
            ids_only=self.pinned_ugc_only,
            use_cache=use_cache,
            site_id=site_id,
            filters=filters,
        )

    def commit(self):
        super().commit()
        if self.ugc_list:
            self.ugc_list.save()


@reversion.register()
class FAQ(Element):  # type: ignore[django-manager-missing]
    """FAQ element with dynamic question/answer pairs."""

    # Store as array of arrays: [[question1, answer1], [question2, answer2], ...]
    items = ArrayField(
        ArrayField(models.CharField(max_length=1000), size=2),
        default=list,
        blank=True,
        help_text=_("FAQ items stored as [question, answer] pairs"),
    )

    def __str__(self):
        return "FAQ"


@reversion.register()
class CinematicFeatured(Element):  # type: ignore[django-manager-missing]
    video_id = models.CharField(
        max_length=30,
        blank=False,
    )

    title = models.CharField(
        max_length=50,
        blank=False,
    )

    description = models.CharField(
        max_length=560,
        blank=False,
    )


@reversion.register()
class PageCollection(Element):  # type: ignore[django-manager-missing]
    collection = AddableForeignKey(
        "pages.PageCollection",
        null=False,
        on_delete=models.deletion.CASCADE,
    )

    shuffle = models.BooleanField(
        default=False,
        help_text="Shuffle the top level pages at random in the collection",
    )

    def __str__(self):
        return f"Page Collection - {self.collection.title}"


@reversion.register()
class StoryListCollection(Element):  # type: ignore[django-manager-missing]
    collection = AddableForeignKey(
        "stories.StoryListCollection",
        null=False,
        on_delete=models.deletion.CASCADE,
    )
    tags = models.CharField(
        blank=True,
        max_length=1000,
        help_text="Comma separated list of tags that get merged with each storylist in the collection to provide extra context",
    )
    limit = models.PositiveIntegerField(null=True, blank=True)
    use_canonical_url = models.BooleanField(
        default=False,
        verbose_name=_("Use canonical URL"),
        help_text=_(
            "Use the story's canonical URL instead of opening on this "
            "site, and open in a new window."
        ),
    )

    def __str__(self):
        return f"Story List Collection - {self.collection.title}"


@reversion.register()
class Socials(Element):  # type: ignore[django-manager-missing]
    pass


@reversion.register()
class Authors(Element):  # type: ignore[django-manager-missing]
    author_collection = AddableForeignKey(
        "pages.AuthorCollection",
        null=False,
        on_delete=models.deletion.CASCADE,
    )
